package communication

import (
	"log"
)

// ButtonAction représente une action de bouton
type ButtonAction struct {
	Index       int
	Name        string
	Description string
	IsCommon    bool // true si c'est un comportement transversal
}

// ButtonHandler est le type pour les fonctions de gestion de boutons
type ButtonHandler func(buttonIndex int) bool // retourne true si le bouton a été géré

// ButtonManager gère les actions communes des boutons
type ButtonManager struct {
	commonHandlers map[int]ButtonHandler // Handlers communs par index de bouton
	modeHandlers   map[int]ButtonHandler // Handlers spécifiques au mode
	debug          bool
}

// NewButtonManager crée une nouvelle instance de ButtonManager
func NewButtonManager() *ButtonManager {
	return &ButtonManager{
		commonHandlers: make(map[int]ButtonHandler),
		modeHandlers:   make(map[int]ButtonHandler),
		debug:          true,
	}
}

// RegisterCommonHandler enregistre un handler commun pour un bouton
func (bm *ButtonManager) RegisterCommonHandler(buttonIndex int, handler <PERSON><PERSON><PERSON><PERSON><PERSON>) {
	bm.commonHandlers[buttonIndex] = handler
	if bm.debug {
		log.Printf("Handler commun enregistré pour le bouton %d", buttonIndex)
	}
}

// RegisterModeHandler enregistre un handler spécifique au mode pour un bouton
func (bm *ButtonManager) RegisterModeHandler(buttonIndex int, handler ButtonHandler) {
	bm.modeHandlers[buttonIndex] = handler
	if bm.debug {
		log.Printf("Handler de mode enregistré pour le bouton %d", buttonIndex)
	}
}

// ClearModeHandlers efface tous les handlers spécifiques au mode
func (bm *ButtonManager) ClearModeHandlers() {
	bm.modeHandlers = make(map[int]ButtonHandler)
	if bm.debug {
		log.Println("Handlers de mode effacés")
	}
}

// HandleButton traite un événement de bouton
// Priorité : handlers spécifiques au mode > handlers communs
func (bm *ButtonManager) HandleButton(buttonIndex int) bool {
	if bm.debug {
		log.Printf("Traitement du bouton %d", buttonIndex)
	}

	// 1. Essayer d'abord le handler spécifique au mode
	if handler, exists := bm.modeHandlers[buttonIndex]; exists {
		if bm.debug {
			log.Printf("Utilisation du handler spécifique au mode pour le bouton %d", buttonIndex)
		}
		return handler(buttonIndex)
	}

	// 2. Sinon, utiliser le handler commun
	if handler, exists := bm.commonHandlers[buttonIndex]; exists {
		if bm.debug {
			log.Printf("Utilisation du handler commun pour le bouton %d", buttonIndex)
		}
		return handler(buttonIndex)
	}

	// 3. Aucun handler trouvé
	if bm.debug {
		log.Printf("Aucun handler trouvé pour le bouton %d", buttonIndex)
	}
	return false
}

// SetDebug active ou désactive les logs de debug
func (bm *ButtonManager) SetDebug(debug bool) {
	bm.debug = debug
}

// GetRegisteredButtons retourne la liste des boutons enregistrés
func (bm *ButtonManager) GetRegisteredButtons() map[int][]string {
	result := make(map[int][]string)
	
	for index := range bm.commonHandlers {
		if result[index] == nil {
			result[index] = []string{}
		}
		result[index] = append(result[index], "common")
	}
	
	for index := range bm.modeHandlers {
		if result[index] == nil {
			result[index] = []string{}
		}
		result[index] = append(result[index], "mode")
	}
	
	return result
}